"""
API views for user authentication and account management
"""

from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.authtoken.models import Token
from django.contrib.auth import authenticate
from django.utils import timezone
from django.contrib.auth import get_user_model
from .models import CustomUser, UserProfile, VerificationCode
from .forms import create_verification_code
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, UserSerializer,
    UserProfileSerializer, VerificationCodeSerializer
)

User = get_user_model()


class RegisterAPIView(APIView):
    """
    API endpoint for user registration
    """
    permission_classes = [AllowAny]
    
    def post(self, request):
        serializer = UserRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            
            # Create user profile
            UserProfile.objects.create(user=user)
            
            # Generate verification code
            verification_code = create_verification_code(user, 'registration')
            
            # Create authentication token
            token, _ = Token.objects.get_or_create(user=user)
            
            return Response({
                'message': 'Registration successful',
                'user': UserSerializer(user).data,
                'token': token.key,
                'verification_code': verification_code.code  # Remove in production
            }, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LoginAPIView(APIView):
    """
    API endpoint for user login
    """
    permission_classes = [AllowAny]
    
    def post(self, request):
        serializer = UserLoginSerializer(data=request.data)
        if serializer.is_valid():
            # Type assertion to help IDE understand validated_data is a dict
            validated_data = serializer.validated_data
            assert isinstance(validated_data, dict), "validated_data should be a dict when serializer is valid"
            phone_number = validated_data['phone_number']
            password = validated_data['password']
            
            user = authenticate(username=phone_number, password=password)
            if user:
                # Update last login IP
                # Type assertion to help IDE understand this is our CustomUser
                custom_user = user
                assert isinstance(custom_user, CustomUser), "User should be CustomUser instance"
                custom_user.last_login_ip = self.get_client_ip(request)
                custom_user.save(update_fields=['last_login_ip'])
                
                # Get or create token
                token, _ = Token.objects.get_or_create(user=user)
                
                return Response({
                    'message': 'Login successful',
                    'user': UserSerializer(user).data,
                    'token': token.key
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'error': 'Invalid credentials'
                }, status=status.HTTP_401_UNAUTHORIZED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class LogoutAPIView(APIView):
    """
    API endpoint for user logout
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        try:
            # Delete the user's token
            request.user.auth_token.delete()
            return Response({
                'message': 'Logout successful'
            }, status=status.HTTP_200_OK)
        except:
            return Response({
                'error': 'Error logging out'
            }, status=status.HTTP_400_BAD_REQUEST)


class ProfileAPIView(APIView):
    """
    API endpoint for getting user profile
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        try:
            profile = request.user.profile
        except UserProfile.DoesNotExist:
            profile = UserProfile.objects.create(user=request.user)
        
        return Response({
            'user': UserSerializer(request.user).data,
            'profile': UserProfileSerializer(profile).data
        }, status=status.HTTP_200_OK)


class UpdateProfileAPIView(APIView):
    """
    API endpoint for updating user profile
    """
    permission_classes = [IsAuthenticated]
    
    def put(self, request):
        try:
            profile = request.user.profile
        except UserProfile.DoesNotExist:
            profile = UserProfile.objects.create(user=request.user)
        
        serializer = UserProfileSerializer(profile, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'message': 'Profile updated successfully',
                'profile': serializer.data
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class VerifyAccountAPIView(APIView):
    """
    API endpoint for account verification
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        if request.user.is_verified:
            return Response({
                'error': 'Account is already verified'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = VerificationCodeSerializer(data=request.data)
        if serializer.is_valid():
            # Type assertion to help IDE understand validated_data is a dict
            validated_data = serializer.validated_data
            assert isinstance(validated_data, dict), "validated_data should be a dict when serializer is valid"
            code = validated_data['code']
            
            try:
                verification_code = VerificationCode.objects.get(
                    user=request.user,
                    code=code,
                    code_type='registration',
                    is_used=False
                )
                
                if verification_code.is_expired:
                    return Response({
                        'error': 'Verification code has expired'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                # Mark code as used
                verification_code.is_used = True
                verification_code.save()
                
                # Mark user as verified
                request.user.is_verified = True
                request.user.save(update_fields=['is_verified'])
                
                return Response({
                    'message': 'Account verified successfully'
                }, status=status.HTTP_200_OK)
                
            except VerificationCode.DoesNotExist:
                return Response({
                    'error': 'Invalid verification code'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ResendVerificationAPIView(APIView):
    """
    API endpoint for resending verification code
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        if request.user.is_verified:
            return Response({
                'error': 'Account is already verified'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Check rate limiting
        if request.user.verification_sent_at:
            time_since_last = timezone.now() - request.user.verification_sent_at
            if time_since_last.total_seconds() < 60:  # 1 minute cooldown
                return Response({
                    'error': 'Please wait before requesting another code'
                }, status=status.HTTP_429_TOO_MANY_REQUESTS)
        
        # Generate new verification code
        verification_code = create_verification_code(request.user, 'registration')
        
        # Update verification sent timestamp
        request.user.verification_sent_at = timezone.now()
        request.user.save(update_fields=['verification_sent_at'])
        
        return Response({
            'message': 'Verification code sent successfully',
            'code': verification_code.code  # Remove in production
        }, status=status.HTTP_200_OK)


class PasswordResetAPIView(APIView):
    """
    API endpoint for password reset request
    """
    permission_classes = [AllowAny]
    
    def post(self, request):
        phone_number = request.data.get('phone_number')
        if not phone_number:
            return Response({
                'error': 'Phone number is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = User.objects.get(phone_number=phone_number)
            
            # Generate verification code for password reset
            verification_code = create_verification_code(user, 'password_reset')
            
            return Response({
                'message': 'Password reset code sent successfully',
                'code': verification_code.code  # Remove in production
            }, status=status.HTTP_200_OK)
            
        except User.DoesNotExist:
            return Response({
                'error': 'No account found with this phone number'
            }, status=status.HTTP_404_NOT_FOUND)


class PasswordChangeAPIView(APIView):
    """
    API endpoint for password change
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        current_password = request.data.get('current_password')
        new_password = request.data.get('new_password')
        
        if not current_password or not new_password:
            return Response({
                'error': 'Current password and new password are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Verify current password
        if not request.user.check_password(current_password):
            return Response({
                'error': 'Current password is incorrect'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Set new password
        request.user.set_password(new_password)
        request.user.save()
        
        # Delete all tokens to force re-login
        Token.objects.filter(user=request.user).delete()
        
        return Response({
            'message': 'Password changed successfully. Please login again.'
        }, status=status.HTTP_200_OK)