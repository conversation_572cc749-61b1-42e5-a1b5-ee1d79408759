"""
Utility functions for accounts app
"""

import random
import string
import hashlib
import secrets
import re
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from datetime import timedelta
from .models import VerificationCode


def generate_secure_token(length=32):
    """
    Generate a cryptographically secure random token
    """
    return secrets.token_urlsafe(length)


def generate_verification_code(length=6):
    """
    Generate a numeric verification code
    """
    return ''.join(random.choices(string.digits, k=length))


def hash_token(token):
    """
    Hash a token using SHA-256
    """
    return hashlib.sha256(token.encode()).hexdigest()


def create_verification_code(user, code_type='registration', expiry_minutes=10):
    """
    Create a new verification code for a user
    """
    # Expire any existing codes of the same type
    VerificationCode.objects.filter(
        user=user,
        code_type=code_type,
        is_used=False
    ).update(is_used=True)
    
    # Create new verification code
    code = generate_verification_code()
    expires_at = timezone.now() + timedelta(minutes=expiry_minutes)
    
    verification_code = VerificationCode.objects.create(
        user=user,
        code=code,
        code_type=code_type,
        expires_at=expires_at
    )
    
    return verification_code


def send_verification_sms(user, code, code_type='registration'):
    """
    Send verification code via SMS
    This is a placeholder function - in production, integrate with SMS provider
    """
    messages = {
        'registration': f'Your Betzide Clone verification code is: {code}. Valid for 10 minutes.',
        'password_reset': f'Your Betzide Clone password reset code is: {code}. Valid for 10 minutes.',
        'phone_verification': f'Your Betika Clone phone verification code is: {code}. Valid for 10 minutes.',
    }
    
    message = messages.get(code_type, f'Your verification code is: {code}')
    
    # TODO: Integrate with SMS provider (e.g., Twilio, Africa's Talking)
    # For now, just log the message
    print(f"SMS to {user.phone_number}: {message}")
    
    return True


def send_verification_email(user, code, code_type='registration'):
    """
    Send verification code via email
    """
    subject_map = {
        'registration': 'Verify Your Betzide Clone Account',
        'password_reset': 'Reset Your Betzide Clone Password',
        'phone_verification': 'Verify Your Phone Number',
    }
    
    template_map = {
        'registration': 'accounts/emails/verification_code.html',
        'password_reset': 'accounts/emails/password_reset_code.html',
        'phone_verification': 'accounts/emails/phone_verification_code.html',
    }
    
    subject = subject_map.get(code_type, 'Verification Code')
    template = template_map.get(code_type, 'accounts/emails/verification_code.html')
    
    context = {
        'user': user,
        'code': code,
        'code_type': code_type,
        'expires_in': '10 minutes'
    }
    
    html_message = render_to_string(template, context)
    plain_message = f"Your verification code is: {code}. This code will expire in 10 minutes."
    
    try:
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_message,
            fail_silently=False,
        )
        return True
    except Exception as e:
        print(f"Error sending email: {e}")
        return False


def validate_phone_number(phone_number):
    """
    Validate phone number format
    """
    # Remove all non-digit characters except +
    cleaned = re.sub(r'[^\d+]', '', phone_number)
    
    # Check if it matches international format
    if re.match(r'^\+?1?\d{9,15}$', cleaned):
        return cleaned
    
    return None


def check_password_strength(password):
    """
    Check password strength and return feedback
    """
    feedback = []
    score = 0
    
    if len(password) >= 8:
        score += 1
    else:
        feedback.append("Password should be at least 8 characters long")
    
    if re.search(r'[A-Z]', password):
        score += 1
    else:
        feedback.append("Password should contain at least one uppercase letter")
    
    if re.search(r'[a-z]', password):
        score += 1
    else:
        feedback.append("Password should contain at least one lowercase letter")
    
    if re.search(r'\d', password):
        score += 1
    else:
        feedback.append("Password should contain at least one number")
    
    if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        score += 1
    else:
        feedback.append("Password should contain at least one special character")
    
    strength_levels = {
        0: 'Very Weak',
        1: 'Weak',
        2: 'Fair',
        3: 'Good',
        4: 'Strong',
        5: 'Very Strong'
    }
    
    return {
        'score': score,
        'strength': strength_levels[score],
        'feedback': feedback
    }


def log_security_event(user, event_type, details=None, ip_address=None):
    """
    Log security-related events
    """
    from django.contrib.admin.models import LogEntry, ADDITION
    from django.contrib.contenttypes.models import ContentType

    # Skip logging if no user is provided, as Django's LogEntry requires a valid user_id
    if not user:
        print(f"Warning: Cannot log security event '{event_type}' - no user provided")
        return

    try:
        LogEntry.objects.log_action(
            user_id=user.id,
            content_type_id=ContentType.objects.get_for_model(user.__class__).pk,
            object_id=user.pk,
            object_repr=str(user),
            action_flag=ADDITION,
            change_message=f"Security Event: {event_type} - {details or ''} - IP: {ip_address or 'Unknown'}"
        )
    except Exception as e:
        print(f"Error logging security event: {e}")


def rate_limit_check(user, action_type, limit_per_hour=5):
    """
    Check if user has exceeded rate limit for specific actions
    """
    from django.core.cache import cache
    
    cache_key = f"rate_limit_{user.id}_{action_type}"
    current_count = cache.get(cache_key, 0)
    
    if current_count >= limit_per_hour:
        return False, f"Rate limit exceeded. Try again in an hour."
    
    # Increment counter
    cache.set(cache_key, current_count + 1, 3600)  # 1 hour expiry
    
    return True, None


def cleanup_expired_codes():
    """
    Clean up expired verification codes
    This should be run as a periodic task
    """
    expired_codes = VerificationCode.objects.filter(
        expires_at__lt=timezone.now(),
        is_used=False
    )
    
    count = expired_codes.count()
    expired_codes.update(is_used=True)
    
    return count