"""
Custom managers for user models
"""

from typing import TYPE_CHECKING
from django.contrib.auth.models import UserManager
from django.core.exceptions import ValidationError
from django.core.validators import validate_email

if TYPE_CHECKING:
    from .models import CustomUser


class CustomUserManager(UserManager["CustomUser"]):
    """
    Custom user manager for CustomUser model
    """
    
    def create_user(self, phone_number, email, password=None, **extra_fields):
        """
        Create and return a regular user with phone number and email
        """
        if not phone_number:
            raise ValueError('The Phone Number field must be set')
        if not email:
            raise ValueError('The Email field must be set')
        
        # Validate email
        try:
            validate_email(email)
        except ValidationError:
            raise ValueError('Invalid email address')
        
        # Normalize email
        email = self.normalize_email(email)
        
        # Create user instance
        user = self.model(
            phone_number=phone_number,
            email=email,
            **extra_fields
        )
        
        # Set password
        user.set_password(password)
        user.save(using=self._db)
        
        return user
    
    def create_superuser(self, phone_number, email, password=None, **extra_fields):
        """
        Create and return a superuser with phone number and email
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_verified', True)
        
        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')
        
        return self.create_user(phone_number, email, password, **extra_fields)
    
    def get_by_natural_key(self, username):
        """
        Allow authentication using phone number
        """
        return self.get(**{self.model.USERNAME_FIELD: username})